/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = anonymous_divination_records   */
/******************************************/
CREATE TABLE `anonymous_divination_records` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `browser_fingerprint` text NOT NULL COMMENT '浏览器指纹',
  `session_id` varchar(36) NOT NULL COMMENT '占卜会话ID',
  `question` text NOT NULL COMMENT '占卜问题',
  `spread_id` varchar(36) DEFAULT NULL COMMENT '牌阵ID',
  `spread_name` varchar(255) DEFAULT NULL COMMENT '牌阵名称',
  `selected_cards` json DEFAULT NULL COMMENT '选中的卡牌',
  `reading_result` json DEFAULT NULL COMMENT '占卜结果',
  `status` varchar(50) DEFAULT 'pending' COMMENT '占卜状态：pending-进行中，completed-已完成，failed-失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ip_address` varchar(100) DEFAULT NULL COMMENT 'IP地址',
  INDEX `idx_fingerprint` (`browser_fingerprint`(255)),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='匿名占卜记录表';
